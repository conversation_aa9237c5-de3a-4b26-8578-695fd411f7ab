import { getManagerAppraisals } from "@/lib/data"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { StatusBadge } from "@/components/status-badge"
import { ArrowRight, Users, UserPlus } from "lucide-react"
import Link from "next/link"
import type { EmployeeAppraisal } from "@/lib/types"

export default async function ManagerDashboardPage() {
  console.log('[DASHBOARD] Loading manager dashboard')

  let appraisals: EmployeeAppraisal[] = []
  try {
    appraisals = await getManagerAppraisals()
    console.log('[DASHBOARD] Loaded', appraisals.length, 'appraisals')
  } catch (error) {
    console.log('[DASHBOARD] Error loading appraisals:', error)
    appraisals = []
  }

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold tracking-tight">My Team</h1>
        <p className="text-muted-foreground">July 2025 appraisal period.</p>
      </div>

      {appraisals.length === 0 ? (
        <Card className="text-center py-12">
          <CardContent>
            <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">You don't have people rn, pls add them</h3>
            <p className="text-muted-foreground mb-6">
              Get started by adding employees to your team to begin the appraisal process.
            </p>
            <Button asChild>
              <Link href="/dashboard/add-people">
                <UserPlus className="mr-2 h-4 w-4" />
                Add People
              </Link>
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
          {appraisals.map((appraisal) => (
            <Card key={appraisal.employeeId}>
              <CardHeader>
                <CardTitle>{appraisal.fullName}</CardTitle>
                <CardDescription>{appraisal.departmentName}</CardDescription>
              </CardHeader>
              <CardContent>
                <StatusBadge status={appraisal.status} />
                {appraisal.status === "submitted" && appraisal.submittedAt && (
                  <p className="mt-2 text-xs text-muted-foreground">
                    Submitted on {new Date(appraisal.submittedAt).toLocaleDateString()}
                  </p>
                )}
              </CardContent>
              <CardFooter>
                <Button size="sm" asChild>
                  <a href={`/dashboard/appraisal/${appraisal.employeeId}`}>
                    {appraisal.status === "not-started" ? "Start Appraisal" : "View / Edit"}
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </a>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
