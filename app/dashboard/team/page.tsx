import { getEmployees, getDepartments } from "@/lib/data"
import { getCurrentUser } from "@/lib/auth"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { RoleGuard } from "@/components/role-guard"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Shield, Star, Users } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import type { Employee } from "@/lib/types"

export default async function TeamPage() {
  console.log('[TEAM PAGE] Loading team page for super-admin')
  const user = await getCurrentUser()

  if (!user) {
    console.log('[TEAM PAGE] No user found, showing auth error')
    return (
      <div className="p-4 sm:p-6">
        <Alert variant="destructive">
          <Shield className="h-4 w-4" />
          <AlertDescription>
            Authentication required. Please sign in to access this page.
          </AlertDescription>
        </Alert>
      </div>
    )
  }

  console.log('[TEAM PAGE] User found:', user.fullName, 'Role:', user.role)

  return (
    <RoleGuard
      allowedRoles={['super-admin']}
      userRole={user.role}
      redirectTo="/dashboard"
    >
      <TeamContent />
    </RoleGuard>
  )
}

async function TeamContent() {
  console.log('[TEAM PAGE] Loading team content for super-admin')
  const employees = await getEmployees()
  const departments = await getDepartments()

  console.log('[TEAM PAGE] Loaded', employees.length, 'employees and', departments.length, 'departments')

  // Group employees by department
  const employeesByDepartment = employees.reduce((acc, employee) => {
    const deptName = employee.departmentName || 'No Department'
    if (!acc[deptName]) {
      acc[deptName] = []
    }
    acc[deptName].push(employee)
    return acc
  }, {} as Record<string, Employee[]>)

  console.log('[TEAM PAGE] Grouped employees by department:', Object.keys(employeesByDepartment))

  return (
    <div className="p-4 sm:p-6">
      <div className="mb-6">
        <div className="flex items-center gap-2 mb-2">
          <Star className="h-6 w-6 text-yellow-500" />
          <h1 className="text-2xl font-bold tracking-tight">Company Team</h1>
        </div>
        <p className="text-muted-foreground">
          Overview of all employees across the company. Super-admin view.
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid gap-4 md:grid-cols-3 mb-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employees.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Departments</CardTitle>
            <Shield className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{departments.length}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Employees</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{employees.filter(e => e.isActive).length}</div>
          </CardContent>
        </Card>
      </div>

      {/* Employees by Department */}
      <div className="space-y-6">
        {Object.entries(employeesByDepartment).map(([departmentName, deptEmployees]) => (
          <Card key={departmentName}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5" />
                {departmentName}
                <Badge variant="secondary" className="ml-auto">
                  {deptEmployees.length} employees
                </Badge>
              </CardTitle>
              <CardDescription>
                All employees in the {departmentName} department
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                {deptEmployees.map((employee) => (
                  <div
                    key={employee.id}
                    className="flex items-center justify-between p-3 border rounded-lg"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">{employee.fullName}</span>
                      <span className="text-sm text-muted-foreground">
                        {employee.payType === 'hourly' 
                          ? `$${employee.hourlyRate}/hr` 
                          : `$${employee.monthlySalary}/month`
                        }
                      </span>
                    </div>
                    <Badge 
                      variant={employee.isActive ? "default" : "secondary"}
                      className={employee.isActive ? "bg-green-100 text-green-800" : ""}
                    >
                      {employee.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
