-- Add super-admin user: <EMAIL>
-- This script adds the user as a manager with super-admin role

-- Generate a UUID for the user (you'll need to replace this with the actual Clerk user ID when available)
-- For now, using a deterministic UUID based on the email
-- In production, this should be the actual Clerk user ID

INSERT INTO managers (user_id, full_name) 
VALUES (
  gen_random_uuid(), -- Replace with actual Clerk user ID: 'user_2xyz...'
  'CJN Automation'
) ON CONFLICT (user_id) DO NOTHING;

-- Add super-admin role
INSERT INTO user_roles (user_id, role)
SELECT user_id, 'super-admin'
FROM managers 
WHERE full_name = 'CJN Automation'
ON CONFLICT (user_id) DO UPDATE SET role = 'super-admin';

-- Query to verify the user was added
SELECT m.user_id, m.full_name, ur.role
FROM managers m
JOIN user_roles ur ON m.user_id = ur.user_id
WHERE m.full_name = 'CJN Automation';