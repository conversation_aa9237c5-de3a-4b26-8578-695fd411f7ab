import { <PERSON>riefcase, Building2, ChevronUp, FileText, Home, Settings, User, Users, CalendarClock, Shield, BarChart3, UserPlus } from "lucide-react"
import Link from "next/link"

import {
  Sidebar,
  SidebarContent,
  Sidebar<PERSON>ooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from "@/components/ui/sidebar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import type { MockUser } from "@/lib/types"
import type { UserRole } from "@/lib/schemas"

// Define navigation items with role-based access control
interface NavItem {
  href: string
  label: string
  icon: any
  roles: UserRole[]
  description?: string
}

const navigationItems: NavItem[] = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: Home,
    roles: ["manager", "accountant", "hr-admin", "super-admin"],
    description: "Overview and quick actions"
  },
  {
    href: "/dashboard",
    label: "My Team",
    icon: Users,
    roles: ["manager"],
    description: "Manage team appraisals"
  },
  {
    href: "/dashboard/approvals",
    label: "Approvals",
    icon: FileText,
    roles: ["accountant", "hr-admin", "super-admin"],
    description: "Review submitted appraisals"
  },
  {
    href: "/dashboard/employees",
    label: "Employees",
    icon: Users,
    roles: ["hr-admin", "super-admin"],
    description: "Manage employee records"
  },
  {
    href: "/dashboard/add-people",
    label: "Add People",
    icon: UserPlus,
    roles: ["super-admin"],
    description: "Add new employees, managers, and departments"
  },
  {
    href: "/dashboard/departments",
    label: "Departments",
    icon: Building2,
    roles: ["hr-admin", "super-admin"],
    description: "Manage departments"
  },
  {
    href: "/dashboard/periods",
    label: "Appraisal Periods",
    icon: CalendarClock,
    roles: ["hr-admin", "super-admin"],
    description: "Manage appraisal periods"
  },
  {
    href: "/dashboard/reports",
    label: "Reports",
    icon: BarChart3,
    roles: ["manager", "hr-admin", "super-admin"],
    description: "View analytics and reports"
  },
  {
    href: "/dashboard/admin",
    label: "System Admin",
    icon: Shield,
    roles: ["super-admin"],
    description: "System administration"
  },
]

// Role hierarchy for access control
const roleHierarchy: Record<UserRole, number> = {
  'manager': 1,
  'accountant': 2,
  'hr-admin': 3,
  'super-admin': 4,
}

function hasAccess(userRole: UserRole, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(userRole)
}

function getRoleBadgeColor(role: UserRole): string {
  switch (role) {
    case 'super-admin': return 'bg-red-100 text-red-800'
    case 'hr-admin': return 'bg-blue-100 text-blue-800'
    case 'accountant': return 'bg-green-100 text-green-800'
    case 'manager': return 'bg-purple-100 text-purple-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

export function AppSidebar({ user }: { user: MockUser }) {
  // Filter navigation items based on user role
  const accessibleItems = navigationItems.filter(item =>
    hasAccess(user.role as UserRole, item.roles)
  )

  return (
    <Sidebar id="navigation" role="navigation" aria-label="Main navigation">
      <SidebarHeader>
        <div className="flex items-center gap-2 p-2">
          <Briefcase className="h-6 w-6 text-primary-foreground" aria-hidden="true" />
          <span className="text-lg font-semibold text-primary-foreground group-data-[collapsible=icon]:hidden">
            AppraisalTool
          </span>
        </div>
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu role="list">
          {accessibleItems.map((item, index) => (
            <SidebarMenuItem key={`${item.href}-${index}`} role="listitem">
              <SidebarMenuButton
                asChild
                tooltip={item.description || item.label}
              >
                <Link
                  href={item.href}
                  aria-label={`${item.label}: ${item.description}`}
                >
                  <item.icon aria-hidden="true" />
                  <span>{item.label}</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          ))}
        </SidebarMenu>
      </SidebarContent>
      <SidebarSeparator />
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton asChild tooltip="Settings">
              <Link href="/dashboard/settings">
                <Settings />
                <span>Settings</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton tooltip={`${user.fullName} (${user.role})`}>
                  <User />
                  <div className="flex flex-col items-start">
                    <span className="text-sm font-medium">{user.fullName}</span>
                    <Badge
                      variant="secondary"
                      className={`text-xs ${getRoleBadgeColor(user.role as UserRole)}`}
                    >
                      {user.role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </div>
                  <ChevronUp className="ml-auto" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent side="top" align="start" className="w-56">
                <DropdownMenuLabel>
                  <div className="flex flex-col space-y-1">
                    <span>{user.fullName}</span>
                    <span className="text-xs text-muted-foreground">
                      {user.role.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </span>
                  </div>
                </DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem>
                  <User className="mr-2 h-4 w-4" />
                  Profile
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <Settings className="mr-2 h-4 w-4" />
                  Settings
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  Sign Out
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  )
}
